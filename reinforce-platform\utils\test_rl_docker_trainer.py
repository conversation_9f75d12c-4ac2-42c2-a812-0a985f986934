#!/usr/bin/env python3
"""
强化学习Docker训练器测试脚本

用于测试RLDockerTrainer的基本功能
"""

import os
import sys
import django
from pathlib import Path

# 添加项目根目录到Python路径
BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(BASE_DIR))

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'reinforce_platform.settings')
django.setup()

from utils.rl_trainer import RLDockerTrainer, create_rl_training_task
from backend_api.models.rl_training import RLTrainingTask
from django.contrib.auth import get_user_model

User = get_user_model()


def test_create_rl_training_task():
    """测试创建强化学习训练任务"""
    print("测试创建强化学习训练任务...")
    
    # 创建测试用户
    user, _ = User.objects.get_or_create(
        username='test_user',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User'
        }
    )
    
    # 测试配置数据
    config_data = {
        "algorithm": {
            "version": "torch-1.8.1",
            "rlType": "PPO"
        },
        "simulation": {
            "dataSource": "内置仿真环境",
            "useExternalEnv": False,
            "externalEnvAddress": ""
        },
        "agent": {
            "sampleTime": "0.1",
            "actionSpace": "离散动作空间",
            "observationSpace": "连续观测空间",
            "rewardFunction": "标准奖励函数"
        },
        "hyperParams": {
            "learningRate": "1e-4",
            "epochs": 1000,
            "batchSize": 64,
            "learningRateStrategy": "余弦衰减",
            "computeType": "fp32",
            "validationRatio": 0.2
        },
        "cluster": {
            "cpuCount": "8",
            "gpuCount": "1"
        },
        "output": {
            "savePath": "/models/rl_training",
            "saveName": "test_rl_model"
        }
    }
    
    try:
        # 创建训练任务
        task = create_rl_training_task(config_data, user)
        print(f"✓ 成功创建训练任务: {task.training_id}")
        print(f"  - 算法类型: {task.rl_type}")
        print(f"  - 训练轮数: {task.epochs}")
        print(f"  - 批次大小: {task.batch_size}")
        print(f"  - 学习率: {task.learning_rate}")
        return task
    except Exception as e:
        print(f"✗ 创建训练任务失败: {e}")
        return None


def test_rl_docker_trainer_init(task):
    """测试RLDockerTrainer初始化"""
    print("\n测试RLDockerTrainer初始化...")
    
    try:
        trainer = RLDockerTrainer(task)
        print(f"✓ 成功初始化RLDockerTrainer")
        print(f"  - 训练任务ID: {trainer.training_task.training_id}")
        print(f"  - 远程目录: {trainer.remote_dir}")
        print(f"  - 脚本源目录: {trainer.script_source_dir}")
        return trainer
    except Exception as e:
        print(f"✗ 初始化RLDockerTrainer失败: {e}")
        return None


def test_generate_training_config(trainer):
    """测试生成训练配置"""
    print("\n测试生成训练配置...")
    
    try:
        config = trainer._generate_training_config()
        print(f"✓ 成功生成训练配置")
        print(f"  - 算法版本: {config['algorithm']['version']}")
        print(f"  - RL类型: {config['algorithm']['rlType']}")
        print(f"  - 数据源: {config['simulation']['dataSource']}")
        print(f"  - 动作空间: {config['agent']['actionSpace']}")
        print(f"  - 学习率: {config['hyperParams']['learningRate']}")
        print(f"  - 训练轮数: {config['hyperParams']['epochs']}")
        return True
    except Exception as e:
        print(f"✗ 生成训练配置失败: {e}")
        return False


def test_trainer_methods(trainer):
    """测试训练器方法（不实际连接资源）"""
    print("\n测试训练器方法...")
    
    try:
        # 测试获取任务ID
        task_id = trainer.get_task_id()
        print(f"✓ 获取任务ID: {task_id}")
        
        # 测试获取任务信息
        task_info = trainer.get_task_info()
        print(f"✓ 获取任务信息: {task_info}")
        
        # 测试获取训练指标（应该返回空字典，因为没有SSH连接）
        metrics = trainer.get_training_metrics()
        print(f"✓ 获取训练指标: {len(metrics)} 个指标")
        
        # 测试获取任务状态
        status = trainer.get_task_status()
        print(f"✓ 获取任务状态: {status}")
        
        return True
    except Exception as e:
        print(f"✗ 测试训练器方法失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试强化学习Docker训练器...")
    print("=" * 50)
    
    # 测试创建训练任务
    task = test_create_rl_training_task()
    if not task:
        print("创建训练任务失败，终止测试")
        return
    
    # 测试初始化训练器
    trainer = test_rl_docker_trainer_init(task)
    if not trainer:
        print("初始化训练器失败，终止测试")
        return
    
    # 测试生成配置
    if not test_generate_training_config(trainer):
        print("生成配置失败")
    
    # 测试训练器方法
    if not test_trainer_methods(trainer):
        print("测试训练器方法失败")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("\n注意：此测试仅验证基本功能，不会实际连接到资源调度平台")
    print("要进行完整测试，需要配置正确的资源调度平台环境")


if __name__ == "__main__":
    main()
