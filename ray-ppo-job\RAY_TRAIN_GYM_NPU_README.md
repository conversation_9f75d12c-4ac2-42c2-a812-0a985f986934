# Ray Train Gym NPU 强化学习训练脚本

## 概述

`ray_train_gym-npu.py` 是一个基于Ray RLlib的强化学习训练脚本，支持NPU和GPU训练环境，能够接收配置文件并输出训练指标。

## 主要修改

### 1. 新增功能

- ✅ **配置文件支持**: 通过`--config`参数接收JSON格式的训练配置
- ✅ **任务ID支持**: 通过`--task_id`参数指定训练任务ID
- ✅ **继续训练**: 通过`--resume`参数支持从检查点继续训练
- ✅ **训练指标输出**: 自动输出训练指标到JSON文件
- ✅ **多算法支持**: 支持PPO、SAC、DDPG等强化学习算法
- ✅ **资源监控**: 实时监控CPU、NPU、内存使用情况

### 2. 新增类和函数

#### TrainingMetricsCallback
替换了原有的NPUDeviceCallback，新增功能：
- 记录训练指标（奖励、损失、熵值等）
- 监控系统资源使用情况
- 输出指标到JSON文件

#### load_training_config()
加载训练配置文件的函数：
```python
def load_training_config(config_path):
    """加载训练配置文件"""
```

#### 修改后的get_config()
根据配置文件生成Ray RLlib配置：
```python
def get_config(args, training_config):
    """根据训练配置生成Ray RLlib配置"""
```

## 使用方法

### 1. 基本用法

```bash
python ray_train_gym-npu.py \
    --config /workspace/rl_training_config.json \
    --task_id rl_task_123 \
    --resume False
```

### 2. 继续训练

```bash
python ray_train_gym-npu.py \
    --config /workspace/rl_training_config.json \
    --task_id rl_task_123 \
    --resume True \
    --checkpoint-path /path/to/checkpoint
```

### 3. 参数说明

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `--config` | str | 是 | 训练配置文件路径 |
| `--task_id` | str | 是 | 训练任务ID |
| `--resume` | bool | 否 | 是否继续训练（默认False） |
| `--checkpoint-path` | str | 否 | 检查点路径（继续训练时使用） |
| `--training-iterations` | int | 否 | 训练迭代次数（默认2000） |

## 配置文件格式

脚本接收的JSON配置文件格式：

```json
{
    "algorithm": {
        "version": "torch-1.8.1",
        "rlType": "PPO"
    },
    "simulation": {
        "dataSource": "内置仿真环境",
        "useExternalEnv": false,
        "externalEnvAddress": ""
    },
    "agent": {
        "sampleTime": "0.1",
        "actionSpace": "离散动作空间",
        "observationSpace": "连续观测空间",
        "rewardFunction": "标准奖励函数"
    },
    "hyperParams": {
        "learningRate": "1e-4",
        "epochs": 1000,
        "batchSize": 64,
        "learningRateStrategy": "余弦衰减",
        "computeType": "fp32",
        "validationRatio": 0.2
    },
    "cluster": {
        "cpuCount": "4",
        "gpuCount": "1"
    },
    "output": {
        "savePath": "/models/rl_training",
        "saveName": "my_rl_model"
    }
}
```

## 输出文件

### 1. 训练指标文件

路径: `/workspace/rl_training_metrics.json`

格式: 每行一个JSON对象
```json
{
    "episode": 100,
    "cumulative_reward": 150.5,
    "average_reward": 150.5,
    "episode_length": 200,
    "policy_loss": 0.001,
    "value_loss": 0.002,
    "entropy": 0.5,
    "cpu_usage": 45.2,
    "npu_usage": 78.1,
    "memory_usage": 62.3,
    "timestamp": "2023-12-01T10:30:00",
    "reward_min": 100.0,
    "reward_max": 200.0
}
```

### 2. 训练日志

Ray RLlib会自动生成训练日志和检查点文件到`~/ray_results/`目录。

## 支持的算法

- **PPO** (Proximal Policy Optimization)
- **SAC** (Soft Actor-Critic)
- **DDPG** (Deep Deterministic Policy Gradient)

## NPU支持

脚本会自动检测NPU环境：
- 如果检测到`torch_npu`，会启用NPU训练
- 自动设置NPU环境变量和设备
- 监控NPU内存使用情况

## 与RLDockerTrainer的集成

此脚本专门为配合`RLDockerTrainer`使用而设计：

1. **RLDockerTrainer**会：
   - 创建训练资源
   - 拷贝必要文件到`/workspace`
   - 安装依赖包
   - 生成配置文件
   - 调用此脚本开始训练

2. **此脚本**会：
   - 读取配置文件
   - 根据配置设置训练参数
   - 输出训练指标供监控
   - 支持训练控制（暂停/继续）

## 测试

使用提供的测试配置文件：

```bash
python ray_train_gym-npu.py \
    --config test_rl_config.json \
    --task_id test_task_001 \
    --training-iterations 10
```

## 注意事项

1. 确保Ray和相关依赖已正确安装
2. NPU环境需要安装`torch_npu`
3. 配置文件路径必须可访问
4. 训练指标文件会追加写入，建议定期清理
5. TensorBoard会自动启动，可通过浏览器查看训练进度

## 故障排除

### 常见问题

1. **配置文件加载失败**
   - 检查文件路径是否正确
   - 验证JSON格式是否有效

2. **NPU初始化失败**
   - 检查`torch_npu`是否安装
   - 验证NPU驱动是否正常

3. **训练指标文件权限错误**
   - 确保`/workspace`目录有写权限
   - 检查磁盘空间是否充足

## 更新日志

- v2.0.0: 重构脚本，支持配置文件和训练指标输出
- v2.1.0: 添加多算法支持和资源监控
- v2.2.0: 优化NPU支持和错误处理
