{"algorithm": {"version": "torch-1.8.1", "rlType": "PPO"}, "simulation": {"dataSource": "内置仿真环境", "useExternalEnv": false, "externalEnvAddress": ""}, "agent": {"sampleTime": "0.1", "actionSpace": "离散动作空间", "observationSpace": "连续观测空间", "rewardFunction": "标准奖励函数"}, "hyperParams": {"learningRate": "1e-4", "epochs": 100, "batchSize": 64, "learningRateStrategy": "余弦衰减", "computeType": "fp32", "validationRatio": 0.2}, "cluster": {"cpuCount": "4", "gpuCount": "1"}, "output": {"savePath": "/models/rl_training", "saveName": "test_rl_model"}}