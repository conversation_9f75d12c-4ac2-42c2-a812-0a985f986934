# 强化学习Docker训练器 (RLDockerTrainer)

## 概述

RLDockerTrainer是一个基于资源调度平台的容器化强化学习训练解决方案，支持NPU和GPU训练环境，提供实时状态监控和日志获取功能。

## 主要特性

- ✅ **容器化训练**: 基于资源调度平台的Docker容器训练
- ✅ **多硬件支持**: 支持NPU和GPU训练环境
- ✅ **实时监控**: 实时状态监控和训练指标获取
- ✅ **训练控制**: 支持启动、暂停、继续、停止训练
- ✅ **日志管理**: 实时日志获取和查看
- ✅ **多算法支持**: 支持PPO、SAC、DDPG等强化学习算法

## 架构设计

### 核心组件

1. **RLDockerTrainer**: 主要的训练器类
2. **RLTrainerManager**: 训练器管理器，负责创建和管理训练器实例
3. **ResourceManager**: 资源管理器，负责与资源调度平台交互

### 训练流程

```
1. 创建资源 → 2. SSH连接 → 3. 准备环境 → 4. 执行训练 → 5. 监控状态
```

## 使用方法

### 1. 创建训练任务

```python
from utils.rl_trainer import create_rl_training_task, RLTrainerManager

# 配置数据
config_data = {
    "algorithm": {
        "version": "torch-1.8.1",
        "rlType": "PPO"
    },
    "simulation": {
        "dataSource": "内置仿真环境",
        "useExternalEnv": False
    },
    "agent": {
        "sampleTime": "0.1",
        "actionSpace": "离散动作空间",
        "observationSpace": "连续观测空间",
        "rewardFunction": "标准奖励函数"
    },
    "hyperParams": {
        "learningRate": "1e-4",
        "epochs": 1000,
        "batchSize": 64,
        "computeType": "fp32"
    },
    "cluster": {
        "cpuCount": "8",
        "gpuCount": "1"
    },
    "output": {
        "savePath": "/models/rl_training",
        "saveName": "my_rl_model"
    }
}

# 创建训练任务
task = create_rl_training_task(config_data, user)
```

### 2. 启动训练

```python
# 创建训练器
trainer = RLTrainerManager.create_trainer(task)

# 启动训练
success = trainer.start()
if success:
    print("训练启动成功")
else:
    print("训练启动失败")
```

### 3. 训练控制

```python
# 暂停训练
trainer.pause()

# 继续训练
trainer.resume()

# 停止训练
trainer.stop()
```

### 4. 获取训练状态和指标

```python
# 获取任务状态
status = trainer.get_task_status()
print(f"训练状态: {status['status']}")

# 获取训练指标
metrics = trainer.get_training_metrics()
print(f"训练指标: {metrics}")

# 获取训练日志
logs = trainer.get_training_logs(lines=100)
print(f"训练日志: {logs}")
```

## API接口

### HTTP接口

- `POST /backend/training/rl/start` - 启动强化学习训练
- `POST /backend/training/rl/{training_id}/pause` - 暂停训练
- `POST /backend/training/rl/{training_id}/resume` - 继续训练
- `POST /backend/training/rl/{training_id}/stop` - 停止训练
- `GET /backend/training/rl/{training_id}/status` - 获取训练状态
- `GET /backend/training/rl/{training_id}/metrics` - 获取训练指标

### 响应格式

```json
{
    "success": true,
    "data": {
        "trainingId": "rl_train_20231201_143022_1234",
        "status": "running",
        "message": "操作成功"
    }
}
```

## 环境要求

### 服务器环境

1. **脚本目录**: `/root/offline_packages` 和 `/root/ray-ppo-job`
2. **训练脚本**: `/root/ray-ppo-job/ray_train_gym-npu.py`
3. **离线包**: `/root/offline_packages/*.whl`

### NPU环境配置

```bash
# 环境变量设置
source ~/.bashrc
source /usr/local/Ascend/ascend-toolkit/set_env.sh
source /usr/local/Ascend/ascend-toolkit/8.0.RC2.2/aarch64-linux/script/set_env.sh
export LD_LIBRARY_PATH=/usr/local/Ascend/driver/lib64/driver/:/usr/local/python3.9.2/lib/:$LD_LIBRARY_PATH
```

## 配置文件格式

训练器会自动生成配置文件 `rl_training_config.json`：

```json
{
    "algorithm": {
        "version": "torch-1.8.1",
        "rlType": "PPO"
    },
    "simulation": {
        "dataSource": "内置仿真环境",
        "useExternalEnv": false,
        "externalEnvAddress": ""
    },
    "agent": {
        "sampleTime": "0.1",
        "actionSpace": "离散动作空间",
        "observationSpace": "连续观测空间",
        "rewardFunction": "标准奖励函数"
    },
    "hyperParams": {
        "learningRate": "1e-4",
        "epochs": 1000,
        "batchSize": 64,
        "learningRateStrategy": "余弦衰减",
        "computeType": "fp32",
        "validationRatio": 0.2
    },
    "cluster": {
        "cpuCount": "8",
        "gpuCount": "1"
    },
    "output": {
        "savePath": "/models/rl_training",
        "saveName": "my_rl_model"
    }
}
```

## 训练指标

训练器会生成 `rl_training_metrics.json` 文件，包含以下指标：

- `episode`: 当前回合数
- `cumulative_reward`: 累积奖励
- `policy_loss`: 策略损失
- `value_loss`: 价值损失
- `entropy`: 熵值
- `cpu_usage`: CPU使用率
- `npu_usage`: NPU使用率
- `memory_usage`: 内存使用率
- `timestamp`: 时间戳

## 故障排除

### 常见问题

1. **SSH连接失败**
   - 检查网络连接
   - 验证SSH端口和密码
   - 确认服务器状态

2. **训练脚本执行失败**
   - 检查脚本路径是否正确
   - 验证环境变量设置
   - 查看训练日志

3. **资源创建失败**
   - 检查资源调度平台状态
   - 验证资源配置参数
   - 确认可用资源

### 日志查看

```python
# 获取详细日志
logs = trainer.get_training_logs(lines=500, mode='all')
print(logs)
```

## 测试

运行测试脚本验证功能：

```bash
cd reinforce-platform/utils
python test_rl_docker_trainer.py
```

## 注意事项

1. 确保资源调度平台环境配置正确
2. 训练脚本和依赖包必须预先部署在服务器上
3. 建议在测试环境中先验证配置
4. 监控资源使用情况，避免资源耗尽
5. 定期清理训练日志和临时文件

## 更新日志

- v1.0.0: 初始版本，支持基本的强化学习训练功能
- v1.1.0: 添加暂停和继续训练功能
- v1.2.0: 优化资源管理和错误处理
