import time
import os
import sys
import json
import psutil
from datetime import datetime
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))
import ray
print("Ray 路径:", ray.__file__)
print("Ray 版本:", ray.__version__)
from ray import tune
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.algorithms.sac import SACConfig
from ray.rllib.algorithms.ddpg import DDPGConfig
from ray.rllib.algorithms import Algorithm
from ray.rllib.callbacks.callbacks import RLlibCallback
from torch import Tensor
import argparse
import subprocess

# 华为NPU支持
try:
    import torch_npu
    NPU_AVAILABLE = True
    print("华为NPU支持已加载")
except ImportError:
    NPU_AVAILABLE = False
    print("华为NPU支持未找到，将使用CPU/GPU")

# 训练指标回调
class TrainingMetricsCallback(RLlibCallback):
    """训练指标回调，记录训练过程中的指标并输出到JSON文件"""

    def __init__(self, metrics_file="/workspace/rl_training_metrics.json"):
        super().__init__()
        self.device_type = None
        self.metrics_file = metrics_file
        self.episode_count = 0

    def on_algorithm_init(self, *, algorithm, **kwargs):
        """算法初始化时设置设备"""
        # 检查是否应该使用NPU
        if NPU_AVAILABLE and hasattr(algorithm.config, 'framework_str'):
            # 尝试获取设备类型从配置
            if hasattr(algorithm.config, '_device_type'):
                self.device_type = algorithm.config._device_type
            else:
                # 默认检查NPU可用性
                import torch
                if hasattr(torch, 'npu') and torch.npu.is_available():
                    self.device_type = 'npu'
                    print("NPU设备回调已激活")

    def on_train_result(self, *, algorithm, result, **kwargs):
        """训练结果回调，记录训练指标"""
        try:
            # 获取基本训练指标
            episode = result.get("training_iteration", self.episode_count)
            self.episode_count = episode

            # 强化学习特有指标
            episode_reward_mean = result.get("episode_reward_mean", 0.0)
            episode_reward_min = result.get("episode_reward_min", 0.0)
            episode_reward_max = result.get("episode_reward_max", 0.0)
            episode_len_mean = result.get("episode_len_mean", 0.0)

            # 策略和价值损失
            policy_loss = result.get("info", {}).get("learner", {}).get("default_policy", {}).get("policy_loss", 0.0)
            vf_loss = result.get("info", {}).get("learner", {}).get("default_policy", {}).get("vf_loss", 0.0)
            entropy = result.get("info", {}).get("learner", {}).get("default_policy", {}).get("entropy", 0.0)

            # 获取系统资源使用情况
            cpu_usage = psutil.cpu_percent()
            memory_usage = psutil.virtual_memory().percent

            # NPU使用情况
            npu_usage = 0.0
            if self.device_type == 'npu':
                import torch
                if hasattr(torch, 'npu'):
                    try:
                        memory_allocated = torch.npu.memory_allocated() / 1024**3  # GB
                        memory_reserved = torch.npu.memory_reserved() / 1024**3   # GB
                        result["custom_metrics"]["npu_memory_allocated_gb"] = memory_allocated
                        result["custom_metrics"]["npu_memory_reserved_gb"] = memory_reserved
                        npu_usage = (memory_allocated / memory_reserved * 100) if memory_reserved > 0 else 0.0
                        print(f"NPU内存使用: 已分配 {memory_allocated:.2f}GB, 已保留 {memory_reserved:.2f}GB")
                    except Exception as e:
                        print(f"获取NPU内存使用情况失败: {e}")

            # 构建指标数据
            metrics_data = {
                "episode": episode,
                "cumulative_reward": episode_reward_mean,
                "average_reward": episode_reward_mean,
                "episode_length": int(episode_len_mean),
                "policy_loss": policy_loss,
                "value_loss": vf_loss,
                "entropy": entropy,
                "cpu_usage": cpu_usage,
                "npu_usage": npu_usage,
                "memory_usage": memory_usage,
                "timestamp": datetime.now().isoformat(),
                "reward_min": episode_reward_min,
                "reward_max": episode_reward_max
            }

            # 输出指标到JSON文件（追加模式）
            with open(self.metrics_file, "a") as f:
                f.write(json.dumps(metrics_data) + "\n")

            # 打印训练进度
            print(f"Episode {episode}: Reward={episode_reward_mean:.2f}, "
                  f"Policy Loss={policy_loss:.4f}, Value Loss={vf_loss:.4f}, "
                  f"CPU={cpu_usage:.1f}%, NPU={npu_usage:.1f}%, Memory={memory_usage:.1f}%")

        except Exception as e:
            print(f"记录训练指标时出错: {e}")

def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", type=str, required=True,
                      help="训练配置文件路径")
    parser.add_argument("--task_id", type=str, required=True,
                      help="训练任务ID")
    parser.add_argument("--resume", type=bool, default=False,
                      help="是否继续训练")
    parser.add_argument("--checkpoint-path", type=str, default="",
                      help="检查点路径，如果不指定则使用随机动作")
    parser.add_argument("--num-env-runners", type=int, default=16,
                      help="环境运行器数量")
    parser.add_argument("--num-learners", type=int, default=1,
                      help="学习器数量")
    parser.add_argument("--num-gpus", type=int, default=0,
                      help="GPU数量")
    parser.add_argument("--num-npus", type=int, default=0,
                      help="华为NPU数量")
    parser.add_argument("--use-npu", action="store_true",
                      help="使用华为NPU进行训练")
    parser.add_argument("--local-dir", type=str, default="~/ray_results",
                      help="结果保存路径")
    parser.add_argument("--tensorboard-dir", type=str, default="~/tensorboard_logs",
                      help="TensorBoard日志路径")
    parser.add_argument("--training-iterations", type=int, default=2000,
                      help="训练迭代次数")

    return parser.parse_args()

def load_training_config(config_path):
    """加载训练配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"成功加载训练配置: {config_path}")
        return config
    except Exception as e:
        print(f"加载训练配置失败: {e}")
        return None

def get_config(args, training_config):
    """根据训练配置生成Ray RLlib配置"""

    # 从配置文件获取参数
    algorithm_config = training_config.get('algorithm', {})
    hyper_params = training_config.get('hyperParams', {})
    cluster_config = training_config.get('cluster', {})
    agent_config = training_config.get('agent', {})

    # 获取算法类型
    rl_type = algorithm_config.get('rlType', 'PPO')

    # 获取超参数
    learning_rate = float(hyper_params.get('learningRate', '1e-4'))
    batch_size = int(hyper_params.get('batchSize', 32))
    epochs = int(hyper_params.get('epochs', 1000))

    # 获取集群配置
    cpu_count = int(cluster_config.get('cpuCount', '4'))
    gpu_count = int(cluster_config.get('gpuCount', '1'))

    # 确定使用的设备类型和数量
    use_npu = gpu_count > 0 and NPU_AVAILABLE
    if use_npu:
        device_type = "npu"
        num_devices = gpu_count
        print(f"使用华为NPU进行训练，NPU数量: {num_devices}")
        args.use_npu = True
        args.num_npus = num_devices
    else:
        device_type = "gpu" if gpu_count > 0 else "cpu"
        num_devices = gpu_count
        print(f"使用{device_type.upper()}进行训练，设备数量: {num_devices}")
        args.use_npu = False
        args.num_gpus = gpu_count

    # 根据算法类型创建配置
    if rl_type == 'PPO':
        config = PPOConfig()
    elif rl_type == 'SAC':
        config = SACConfig()
    elif rl_type == 'DDPG':
        config = DDPGConfig()
    else:
        print(f"不支持的算法类型: {rl_type}，使用默认PPO")
        config = PPOConfig()

    # 基础配置
    config = (
        config
        .environment("Pendulum-v1")  # 可以根据配置文件修改环境
        .framework("torch")
        .env_runners(
            num_env_runners=max(1, cpu_count // 2),
            rollout_fragment_length='auto',
            num_envs_per_env_runner=1,
            num_cpus_per_env_runner=1,
            max_env_runner_restarts=0,
        )
        .learners(
            num_learners=1,
            num_cpus_per_learner=1,
            num_gpus_per_learner=0 if use_npu else min(1, gpu_count),
            custom_resources_per_learner={"NPU": 1} if use_npu else {}
        )
        .training(
            gamma=0.99,
            lr=learning_rate,
            train_batch_size=max(512, batch_size * 16),
            model={
                "fcnet_hiddens": [256, 256, 128],
                "fcnet_activation": "relu",
                "vf_share_layers": False,
                "use_lstm": True,
                "lstm_cell_size": 128,
                "max_seq_len": 32,
            },
        )
        .evaluation(
            evaluation_interval=10,
            evaluation_duration=20,
            evaluation_num_workers=max(1, cpu_count // 4),
            evaluation_config={
                "render_mode": "txt",
                "explore": False,
            },
        )
    )

    # PPO特定配置
    if rl_type == 'PPO':
        config = config.training(
            lambda_=0.95,
            clip_param=0.2,
            vf_clip_param=10.0,
            entropy_coeff=0.01,
            num_sgd_iter=10,
            minibatch_size=max(64, batch_size),
        )

    # 添加训练指标回调
    config = config.callbacks(TrainingMetricsCallback)

    # 华为NPU特定配置
    if args.use_npu and NPU_AVAILABLE:
        # 添加设备类型标记到配置
        config._device_type = "npu"

        # 添加NPU特定的训练配置
        config = config.training(
            # NPU优化配置
            optimizer={
                "type": "Adam",
                "eps": 1e-7,  # NPU对数值稳定性要求更高
            }
        )

        # 设置NPU环境变量
        os.environ.setdefault("ASCEND_RT_VISIBLE_DEVICES", "0")
        # 设置PyTorch默认设备为NPU
        import torch
        if hasattr(torch, 'npu') and torch.npu.is_available():
            torch.npu.set_device(0)
            print(f"设置默认NPU设备: {torch.npu.current_device()}")
    else:
        config._device_type = "gpu" if args.num_gpus > 0 else "cpu"

    # 清空探索配置
    config.exploration_config = {}
    return config

if __name__ == "__main__":
    args = parse_args()

    # 加载训练配置
    training_config = load_training_config(args.config)
    if training_config is None:
        print("无法加载训练配置，退出")
        sys.exit(1)

    print(f"训练任务ID: {args.task_id}")
    print(f"是否继续训练: {args.resume}")

    # 从配置获取训练参数
    hyper_params = training_config.get('hyperParams', {})
    algorithm_config = training_config.get('algorithm', {})

    # 设置训练迭代次数
    training_iterations = int(hyper_params.get('epochs', 1000))
    rl_type = algorithm_config.get('rlType', 'PPO')

    timestamp = time.strftime("%Y%m%d-%H%M%S")
    experiment_name = f"{rl_type}-{args.task_id}-{timestamp}"

    # 清理旧的指标文件（如果不是继续训练）
    metrics_file = "/workspace/rl_training_metrics.json"
    if not args.resume and os.path.exists(metrics_file):
        os.remove(metrics_file)
        print("已清理旧的训练指标文件")

    # 检查NPU可用性
    if hasattr(args, 'use_npu') and args.use_npu and not NPU_AVAILABLE:
        print("警告：要求使用NPU但torch_npu未安装，将回退到GPU/CPU训练")
        args.use_npu = False

    # 启动tensorboard
    try:
        subprocess.Popen(['tensorboard', '--logdir', f'~/ray_results/{experiment_name}', '--bind_all'],
                        stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("TensorBoard已启动")
    except Exception as e:
        print(f"启动TensorBoard失败: {e}")

    # 初始化Ray
    ray.init()

    # NPU设备验证和设置
    if hasattr(args, 'use_npu') and args.use_npu and NPU_AVAILABLE:
        import torch
        if hasattr(torch, 'npu') and torch.npu.is_available():
            print(f"NPU设备数量: {torch.npu.device_count()}")
            for i in range(torch.npu.device_count()):
                print(f"NPU {i}: {torch.npu.get_device_name(i)}")
            print("NPU设备验证成功")
        else:
            print("警告: NPU设备不可用")
            args.use_npu = False

    # 获取配置
    config = get_config(args, training_config)

    # 设置检查点路径（用于继续训练）
    restore_path = None
    if args.resume and args.checkpoint_path:
        restore_path = args.checkpoint_path
        print(f"继续训练，从检查点恢复: {restore_path}")

    # 运行训练
    try:
        tune.run(
            rl_type,
            config=config,
            resume="LOCAL" if args.resume else False,
            restore=restore_path,
            stop={
                "training_iteration": training_iterations,
                "episode_reward_mean": 800,  # 可以根据配置调整
            },
            checkpoint_freq=50,
            checkpoint_at_end=True,
            local_dir=os.path.expanduser("~/ray_results"),
            name=experiment_name,
            verbose=2,
        )
        print("训练完成")
    except Exception as e:
        print(f"训练过程中出现错误: {e}")
        sys.exit(1)
    finally:
        # 清理资源
        ray.shutdown()