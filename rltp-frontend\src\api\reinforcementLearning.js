/**
 * 强化学习训练相关API接口
 */
import { api } from 'boot/axios'

// 基础URL
const BASE_URL = '/backend/training/rl'

/**
 * 强化学习训练API服务
 */
export const rlTrainingApi = {
  /**
   * 开始训练
   * @param {Object} config - 训练配置
   * @returns {Promise}
   */
  async startTraining(config) {
    try {
      const response = await api.post(`${BASE_URL}/start`, config)
      return response.data
    } catch (error) {
      console.error('开始强化学习训练失败:', error)
      throw error
    }
  },

  /**
   * 停止训练
   * @param {string} trainingId - 训练任务ID
   * @returns {Promise}
   */
  async stopTraining(trainingId) {
    try {
      const response = await api.post(`${BASE_URL}/${trainingId}/stop`)
      return response.data
    } catch (error) {
      console.error('停止强化学习训练失败:', error)
      throw error
    }
  },

  /**
   * 暂停训练
   * @param {string} trainingId - 训练任务ID
   * @returns {Promise}
   */
  async pauseTraining(trainingId) {
    try {
      const response = await api.post(`${BASE_URL}/${trainingId}/pause`)
      return response.data
    } catch (error) {
      console.error('暂停强化学习训练失败:', error)
      throw error
    }
  },

  /**
   * 继续训练
   * @param {string} trainingId - 训练任务ID
   * @returns {Promise}
   */
  async resumeTraining(trainingId) {
    try {
      const response = await api.post(`${BASE_URL}/${trainingId}/resume`)
      return response.data
    } catch (error) {
      console.error('继续强化学习训练失败:', error)
      throw error
    }
  },

  /**
   * 获取训练状态
   * @param {string} trainingId - 训练任务ID
   * @returns {Promise}
   */
  async getTrainingStatus(trainingId) {
    try {
      const response = await api.get(`${BASE_URL}/${trainingId}/status`)
      return response.data
    } catch (error) {
      console.error('获取强化学习训练状态失败:', error)
      throw error
    }
  },

  /**
   * 获取训练指标
   * @param {string} trainingId - 训练任务ID
   * @returns {Promise}
   */
  async getTrainingMetrics(trainingId) {
    try {
      const response = await api.get(`${BASE_URL}/${trainingId}/metrics`)
      return response.data
    } catch (error) {
      console.error('获取强化学习训练指标失败:', error)
      throw error
    }
  },

  /**
   * 获取训练资源使用情况
   * @param {string} trainingId - 训练任务ID
   * @returns {Promise}
   */
  async getTrainingResources(trainingId) {
    try {
      const response = await api.get(`${BASE_URL}/${trainingId}/resources`)
      return response.data
    } catch (error) {
      console.error('获取强化学习训练资源失败:', error)
      throw error
    }
  },

  /**
   * 获取模型列表
   * @param {string} trainingId - 训练任务ID
   * @returns {Promise}
   */
  async getModelList(trainingId) {
    try {
      const response = await api.get(`${BASE_URL}/${trainingId}/models`)
      return response.data
    } catch (error) {
      console.error('获取强化学习模型列表失败:', error)
      throw error
    }
  },

  /**
   * 获取模型信息
   * @param {string} trainingId - 训练任务ID
   * @returns {Promise}
   */
  async getModelInfo(trainingId) {
    try {
      const response = await api.get(`${BASE_URL}/${trainingId}/model/info`)
      return response.data
    } catch (error) {
      console.error('获取强化学习模型信息失败:', error)
      throw error
    }
  },

  /**
   * 下载模型
   * @param {string} trainingId - 训练任务ID
   * @param {string} localPath - 本地保存路径
   * @returns {Promise}
   */
  async downloadModel(trainingId, localPath) {
    try {
      const response = await api.post(`${BASE_URL}/${trainingId}/model/download`, {
        localPath
      })
      return response.data
    } catch (error) {
      console.error('下载强化学习模型失败:', error)
      throw error
    }
  },

  /**
   * 保存训练配置
   * @param {Object} config - 配置数据
   * @returns {Promise}
   */
  async saveConfig(config) {
    try {
      const response = await api.post(`${BASE_URL}/config/save`, config)
      return response.data
    } catch (error) {
      console.error('保存强化学习训练配置失败:', error)
      throw error
    }
  },

  /**
   * 导入训练配置
   * @param {File} file - 配置文件
   * @returns {Promise}
   */
  async importConfig(file) {
    try {
      const formData = new FormData()
      formData.append('file', file)
      const response = await api.post(`${BASE_URL}/config/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      return response.data
    } catch (error) {
      console.error('导入强化学习训练配置失败:', error)
      throw error
    }
  },

  /**
   * 获取配置列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getConfigList(params = {}) {
    try {
      const response = await api.get(`${BASE_URL}/config/list`, { params })
      return response.data
    } catch (error) {
      console.error('获取强化学习训练配置列表失败:', error)
      throw error
    }
  },

  /**
   * 获取训练任务列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  async getTaskList(params = {}) {
    try {
      const response = await api.get(`${BASE_URL}/tasks`, { params })
      return response.data
    } catch (error) {
      console.error('获取强化学习训练任务列表失败:', error)
      throw error
    }
  }
}

export default rlTrainingApi
