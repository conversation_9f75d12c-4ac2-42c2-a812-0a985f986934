# 强化学习训练日志API接口文档

## 📋 概述

这些接口用于获取和实时监控强化学习训练过程中的日志信息，包括训练日志、错误日志、Ray日志等。

## 🔗 接口列表

### 1. 获取强化学习训练日志 (静态)

**接口路径**: `/backend/training/rl/{training_id}/logs`  
**请求方法**: `GET`  
**权限要求**: 需要登录认证

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `lines` | int | 否 | 100 | 获取的行数 |
| `mode` | string | 否 | tail | 获取模式：`tail`(最后几行)、`head`(前几行)、`all`(全部) |
| `search` | string | 否 | - | 搜索关键词 |
| `log_type` | string | 否 | all | 日志类型：`training`(训练日志)、`error`(错误日志)、`all`(全部日志) |

#### 调用示例

```javascript
// 获取最后100行训练日志
fetch('/backend/training/rl/rl_train_20231201_143022_1234/logs?lines=100&mode=tail&log_type=training', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('日志内容:', data.data.logs);
    console.log('日志信息:', data.data.log_info);
});

// 搜索包含"reward"的日志
fetch('/backend/training/rl/rl_train_20231201_143022_1234/logs?search=reward&lines=50', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('搜索结果:', data.data.logs);
});

// 获取错误日志
fetch('/backend/training/rl/rl_train_20231201_143022_1234/logs?log_type=error&mode=all', {
    headers: { 'Authorization': 'Bearer your_token' }
})
.then(response => response.json())
.then(data => {
    console.log('错误日志:', data.data.logs);
});
```

#### 响应格式

```json
{
    "success": true,
    "data": {
        "training_id": "rl_train_20231201_143022_1234",
        "logs": [
            "[TRAINING] 2023-12-01 14:30:22 - Episode 1: Starting training...",
            "[TRAINING] 2023-12-01 14:30:23 - Episode 1: Reward = 15.6",
            "[TRAINING] 2023-12-01 14:30:24 - Episode 1: Policy loss = 0.001",
            "[ERROR] 2023-12-01 14:30:25 - Warning: Low reward detected"
        ],
        "log_type": "all",
        "mode": "tail",
        "lines_requested": 100,
        "search_pattern": "",
        "timestamp": "2023-12-01T14:30:25.123456",
        "task_status": "running",
        "log_info": {
            "total_files": 3,
            "file_names": ["training", "error", "ray"],
            "status": "success"
        },
        "total_lines": 1250,
        "file_size": 52480
    }
}
```

### 2. 强化学习训练日志实时流 (SSE)

**接口路径**: `/backend/training/rl/{training_id}/log-stream`  
**请求方法**: `GET`  
**权限要求**: 需要登录认证  
**响应类型**: `text/event-stream`

#### 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `log_type` | string | 否 | training | 日志类型：`training`、`error`、`ray` |
| `follow` | boolean | 否 | true | 是否持续跟踪日志 |

#### 调用示例

```javascript
// 建立SSE连接获取实时训练日志
const eventSource = new EventSource('/backend/training/rl/rl_train_20231201_143022_1234/log-stream?log_type=training');

eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch(data.type) {
        case 'connected':
            console.log('日志流连接已建立');
            break;
        case 'log':
            console.log(`[${data.log_type}] ${data.content}`);
            // 将日志添加到页面显示
            appendLogToDisplay(data.content);
            break;
        case 'status_change':
            console.log(`训练状态变更: ${data.status}`);
            updateTrainingStatus(data.status);
            break;
        case 'heartbeat':
            console.log('心跳信号');
            break;
        case 'error':
            console.error('日志流错误:', data.message);
            break;
        case 'disconnected':
            console.log('日志流连接已断开');
            break;
    }
};

eventSource.onerror = function(event) {
    console.error('SSE连接错误:', event);
};

// 关闭连接
// eventSource.close();
```

#### SSE事件类型

| 事件类型 | 说明 | 数据格式 |
|----------|------|----------|
| `connected` | 连接建立 | `{"type": "connected", "training_id": "...", "timestamp": 1234567890}` |
| `log` | 新日志内容 | `{"type": "log", "content": "...", "log_type": "training", "timestamp": 1234567890}` |
| `status_change` | 训练状态变更 | `{"type": "status_change", "status": "completed", "timestamp": 1234567890}` |
| `heartbeat` | 心跳信号 | `{"type": "heartbeat", "timestamp": 1234567890}` |
| `error` | 错误信息 | `{"type": "error", "message": "...", "timestamp": 1234567890}` |
| `disconnected` | 连接断开 | `{"type": "disconnected", "timestamp": 1234567890}` |

## 📊 日志类型说明

### 1. 训练日志 (training)
- **文件位置**: `/workspace/rl_logs/{training_id}/training.log`
- **内容**: 强化学习训练过程中的主要信息
- **格式示例**:
  ```
  2023-12-01 14:30:22 - Episode 1: Starting training...
  2023-12-01 14:30:23 - Episode 1: Action taken: [0.5, -0.2, 0.8]
  2023-12-01 14:30:24 - Episode 1: Reward = 15.6, Cumulative = 15.6
  2023-12-01 14:30:25 - Episode 1: Policy loss = 0.001, Value loss = 0.002
  ```

### 2. 错误日志 (error)
- **文件位置**: `/workspace/rl_logs/{training_id}/error.log`
- **内容**: 训练过程中的错误和警告信息
- **格式示例**:
  ```
  2023-12-01 14:30:25 - WARNING: Low reward detected in episode 1
  2023-12-01 14:31:10 - ERROR: Environment reset failed, retrying...
  2023-12-01 14:32:05 - INFO: Environment reset successful
  ```

### 3. Ray日志 (ray)
- **文件位置**: `/tmp/ray/session_latest/logs/worker*.log`
- **内容**: Ray框架的运行日志
- **格式示例**:
  ```
  2023-12-01 14:30:20 - Ray worker started
  2023-12-01 14:30:21 - PPO algorithm initialized
  2023-12-01 14:30:22 - Training iteration 1 started
  ```

## 🔍 搜索功能

支持在日志中搜索特定关键词：

```javascript
// 搜索包含"reward"的日志行
fetch('/backend/training/rl/rl_train_20231201_143022_1234/logs?search=reward&lines=100')

// 搜索错误相关的日志
fetch('/backend/training/rl/rl_train_20231201_143022_1234/logs?search=error&log_type=all')

// 搜索特定episode的日志
fetch('/backend/training/rl/rl_train_20231201_143022_1234/logs?search=Episode%2050&mode=all')
```

## 🚨 错误处理

### 常见错误码

| 错误码 | HTTP状态码 | 说明 | 解决方案 |
|--------|------------|------|----------|
| `TASK_NOT_FOUND` | 404 | 训练任务不存在 | 检查training_id是否正确 |
| `INVALID_PARAMETER` | 400 | 参数错误 | 检查请求参数格式 |
| `INTERNAL_ERROR` | 500 | 内部服务器错误 | 联系管理员 |

### 错误响应格式

```json
{
    "success": false,
    "error": {
        "code": "TASK_NOT_FOUND",
        "message": "强化学习训练任务 rl_train_20231201_143022_1234 不存在"
    }
}
```

## 💡 使用建议

1. **实时监控**: 使用SSE接口进行实时日志监控
2. **历史查看**: 使用静态接口查看历史日志
3. **错误排查**: 优先查看error类型的日志
4. **性能分析**: 搜索"reward"、"loss"等关键词分析训练效果
5. **分页获取**: 对于大量日志，使用lines参数分页获取

## 🔧 集成示例

### React组件示例

```jsx
import React, { useState, useEffect } from 'react';

const RLTrainingLogs = ({ trainingId }) => {
    const [logs, setLogs] = useState([]);
    const [isConnected, setIsConnected] = useState(false);

    useEffect(() => {
        // 建立SSE连接
        const eventSource = new EventSource(`/backend/training/rl/${trainingId}/log-stream`);
        
        eventSource.onmessage = (event) => {
            const data = JSON.parse(event.data);
            
            if (data.type === 'log') {
                setLogs(prev => [...prev, data.content]);
            } else if (data.type === 'connected') {
                setIsConnected(true);
            }
        };

        return () => {
            eventSource.close();
            setIsConnected(false);
        };
    }, [trainingId]);

    return (
        <div>
            <div>连接状态: {isConnected ? '已连接' : '未连接'}</div>
            <div className="log-container">
                {logs.map((log, index) => (
                    <div key={index} className="log-line">{log}</div>
                ))}
            </div>
        </div>
    );
};
```

这套强化学习日志API提供了完整的日志管理功能，支持实时监控和历史查看，便于开发者调试和监控强化学习训练过程！
